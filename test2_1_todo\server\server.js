const express = require('express');
const cors = require('cors');
const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 模拟用户数据
const users = [
    { username: 'admin', password: '123456' }
];

// 登录API
app.post('/api/login', (req, res) => {
    const { username, password } = req.body;
    const user = users.find(u => u.username === username && u.password === password);
    
    if (user) {
        const token = 'jwt-token-' + Math.random();
        res.json({
            success: true,
            data: {
                token,
                username: user.username
            }
        });
    } else {
        res.json({
            success: false,
            message: '用户名或密码错误'
        });
    }
});

// 获取食物列表API
app.get('/api/foods', (req, res) => {
    const foods = require('../data/foods.json');
    res.json({
        success: true,
        data: foods
    });
});

const PORT = 3000;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
