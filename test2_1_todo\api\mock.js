// 模拟API服务
export const mockApi = {
    // 登录API
    async login(username, password) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 模拟简单的用户验证
        if (username === 'admin' && password === '123456') {
            const token = 'mock-jwt-token-' + Math.random();
            return {
                success: true,
                data: {
                    token,
                    username: 'admin'
                }
            };
        }
        
        return {
            success: false,
            message: '用户名或密码错误'
        };
    },

    // 获取食物列表API
    async getFoods() {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 从本地JSON文件获取数据
        const response = await fetch('/data/foods.json');
        const foods = await response.json();
        return {
            success: true,
            data: foods
        };
    }
};
